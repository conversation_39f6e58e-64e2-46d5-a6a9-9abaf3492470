import {Platform, Alert, Linking} from 'react-native';
import {
  PERMISSIONS,
  RESULTS,
  request,
  check,
  openSettings,
  Permission,
} from 'react-native-permissions';

export interface PermissionResult {
  granted: boolean;
  message?: string;
}

class PermissionService {
  private getPhotoPermission(): Permission {
    if (Platform.OS === 'ios') {
      return PERMISSIONS.IOS.PHOTO_LIBRARY;
    } else {
      // Android 13+ 使用细分权限
      if (Platform.Version >= 33) {
        return PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
      } else {
        return PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
      }
    }
  }

  async checkPhotoPermission(): Promise<PermissionResult> {
    try {
      const permission = this.getPhotoPermission();
      const result = await check(permission);
      
      switch (result) {
        case RESULTS.GRANTED:
          return {granted: true};
        case RESULTS.DENIED:
          return {granted: false, message: '相册权限被拒绝'};
        case RESULTS.BLOCKED:
          return {granted: false, message: '相册权限被永久拒绝，请在设置中手动开启'};
        case RESULTS.UNAVAILABLE:
          return {granted: false, message: '设备不支持相册功能'};
        default:
          return {granted: false, message: '未知权限状态'};
      }
    } catch (error) {
      console.error('检查相册权限失败:', error);
      return {granted: false, message: '检查权限时发生错误'};
    }
  }

  async requestPhotoPermission(): Promise<PermissionResult> {
    try {
      const permission = this.getPhotoPermission();
      const result = await request(permission);
      
      switch (result) {
        case RESULTS.GRANTED:
          return {granted: true};
        case RESULTS.DENIED:
          return {granted: false, message: '用户拒绝了相册权限'};
        case RESULTS.BLOCKED:
          this.showPermissionBlockedAlert();
          return {granted: false, message: '相册权限被永久拒绝'};
        case RESULTS.UNAVAILABLE:
          return {granted: false, message: '设备不支持相册功能'};
        default:
          return {granted: false, message: '未知权限状态'};
      }
    } catch (error) {
      console.error('请求相册权限失败:', error);
      return {granted: false, message: '请求权限时发生错误'};
    }
  }

  private showPermissionBlockedAlert(): void {
    Alert.alert(
      '权限被拒绝',
      '相册权限已被永久拒绝，请在设置中手动开启权限以使用此功能。',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '去设置',
          onPress: () => {
            openSettings().catch(() => {
              console.warn('无法打开设置页面');
            });
          },
        },
      ],
    );
  }

  async ensurePhotoPermission(): Promise<PermissionResult> {
    // 首先检查当前权限状态
    const checkResult = await this.checkPhotoPermission();
    
    if (checkResult.granted) {
      return checkResult;
    }
    
    // 如果权限被永久拒绝，直接返回
    if (checkResult.message?.includes('永久拒绝')) {
      this.showPermissionBlockedAlert();
      return checkResult;
    }
    
    // 请求权限
    return await this.requestPhotoPermission();
  }
}

export default new PermissionService();
