import {Platform} from 'react-native';
import {PERMISSIONS, RESULTS} from 'react-native-permissions';
import PermissionService from '../src/services/PermissionService';

// Mock react-native-permissions
jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    IOS: {
      PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
    },
    ANDROID: {
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      READ_MEDIA_IMAGES: 'android.permission.READ_MEDIA_IMAGES',
    },
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    UNAVAILABLE: 'unavailable',
  },
  check: jest.fn(),
  request: jest.fn(),
  openSettings: jest.fn(),
}));

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android',
    Version: 30,
  },
  Alert: {
    alert: jest.fn(),
  },
  Linking: {
    openSettings: jest.fn(),
  },
}));

describe('PermissionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkPhotoPermission', () => {
    it('should return granted when permission is granted', async () => {
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.GRANTED);

      const result = await PermissionService.checkPhotoPermission();

      expect(result.granted).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should return denied when permission is denied', async () => {
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.DENIED);

      const result = await PermissionService.checkPhotoPermission();

      expect(result.granted).toBe(false);
      expect(result.message).toBe('相册权限被拒绝');
    });

    it('should return blocked when permission is blocked', async () => {
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.BLOCKED);

      const result = await PermissionService.checkPhotoPermission();

      expect(result.granted).toBe(false);
      expect(result.message).toBe('相册权限被永久拒绝，请在设置中手动开启');
    });
  });

  describe('requestPhotoPermission', () => {
    it('should return granted when user grants permission', async () => {
      const {request} = require('react-native-permissions');
      request.mockResolvedValue(RESULTS.GRANTED);

      const result = await PermissionService.requestPhotoPermission();

      expect(result.granted).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should return denied when user denies permission', async () => {
      const {request} = require('react-native-permissions');
      request.mockResolvedValue(RESULTS.DENIED);

      const result = await PermissionService.requestPhotoPermission();

      expect(result.granted).toBe(false);
      expect(result.message).toBe('用户拒绝了相册权限');
    });
  });

  describe('ensurePhotoPermission', () => {
    it('should return granted if already has permission', async () => {
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.GRANTED);

      const result = await PermissionService.ensurePhotoPermission();

      expect(result.granted).toBe(true);
    });

    it('should request permission if not granted', async () => {
      const {check, request} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.DENIED);
      request.mockResolvedValue(RESULTS.GRANTED);

      const result = await PermissionService.ensurePhotoPermission();

      expect(check).toHaveBeenCalled();
      expect(request).toHaveBeenCalled();
      expect(result.granted).toBe(true);
    });
  });

  describe('platform specific permissions', () => {
    it('should use iOS photo library permission on iOS', async () => {
      // Mock iOS platform
      Platform.OS = 'ios';
      
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.GRANTED);

      await PermissionService.checkPhotoPermission();

      expect(check).toHaveBeenCalledWith(PERMISSIONS.IOS.PHOTO_LIBRARY);
    });

    it('should use Android READ_MEDIA_IMAGES on Android 13+', async () => {
      // Mock Android 13+
      Platform.OS = 'android';
      Platform.Version = 33;
      
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.GRANTED);

      await PermissionService.checkPhotoPermission();

      expect(check).toHaveBeenCalledWith(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES);
    });

    it('should use Android READ_EXTERNAL_STORAGE on Android < 13', async () => {
      // Mock Android < 13
      Platform.OS = 'android';
      Platform.Version = 30;
      
      const {check} = require('react-native-permissions');
      check.mockResolvedValue(RESULTS.GRANTED);

      await PermissionService.checkPhotoPermission();

      expect(check).toHaveBeenCalledWith(PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE);
    });
  });
});
