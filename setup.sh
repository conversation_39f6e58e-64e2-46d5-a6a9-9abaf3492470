#!/bin/bash

echo "🚀 开始设置相册自动获取应用..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ npm 版本: $(npm --version)"

# 安装依赖
echo "📦 安装项目依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 检查平台
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 检测到 macOS，设置 iOS 环境..."
    
    # 检查CocoaPods
    if ! command -v pod &> /dev/null; then
        echo "⚠️  CocoaPods 未安装，正在安装..."
        sudo gem install cocoapods
    fi
    
    echo "✅ CocoaPods 版本: $(pod --version)"
    
    # 安装iOS依赖
    if [ -d "ios" ]; then
        echo "📱 安装 iOS 依赖..."
        cd ios && pod install && cd ..
        echo "✅ iOS 依赖安装完成"
    else
        echo "⚠️  ios 目录不存在，跳过 iOS 设置"
    fi
fi

# 检查Android环境
if [ -d "$ANDROID_HOME" ]; then
    echo "🤖 检测到 Android SDK"
    echo "✅ ANDROID_HOME: $ANDROID_HOME"
else
    echo "⚠️  ANDROID_HOME 未设置，请确保已安装 Android Studio 并设置环境变量"
fi

# 运行测试
echo "🧪 运行测试..."
npm test -- --watchAll=false

if [ $? -ne 0 ]; then
    echo "⚠️  测试失败，但项目设置已完成"
else
    echo "✅ 测试通过"
fi

echo ""
echo "🎉 项目设置完成！"
echo ""
echo "📱 运行应用:"
echo "  Android: npm run android"
echo "  iOS:     npm run ios"
echo ""
echo "🔧 开发命令:"
echo "  启动Metro: npm start"
echo "  运行测试:   npm test"
echo "  代码检查:   npm run lint"
echo ""
echo "📚 更多信息请查看 README.md"
