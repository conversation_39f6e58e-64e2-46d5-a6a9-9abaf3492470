@echo off
echo 🚀 开始设置相册自动获取应用...

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version

REM 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

echo ✅ npm 版本:
npm --version

REM 安装依赖
echo 📦 安装项目依赖...
npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成

REM 检查Android环境
if defined ANDROID_HOME (
    echo 🤖 检测到 Android SDK
    echo ✅ ANDROID_HOME: %ANDROID_HOME%
) else (
    echo ⚠️  ANDROID_HOME 未设置，请确保已安装 Android Studio 并设置环境变量
)

REM 运行测试
echo 🧪 运行测试...
npm test -- --watchAll=false

if %errorlevel% neq 0 (
    echo ⚠️  测试失败，但项目设置已完成
) else (
    echo ✅ 测试通过
)

echo.
echo 🎉 项目设置完成！
echo.
echo 📱 运行应用:
echo   Android: npm run android
echo   iOS:     npm run ios
echo.
echo 🔧 开发命令:
echo   启动Metro: npm start
echo   运行测试:   npm test
echo   代码检查:   npm run lint
echo.
echo 📚 更多信息请查看 README.md
pause
