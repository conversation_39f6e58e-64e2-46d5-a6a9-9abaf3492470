import {Alert, ToastAndroid, Platform} from 'react-native';

export enum ErrorType {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  PERMISSION_BLOCKED = 'PERMISSION_BLOCKED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface ErrorInfo {
  type: ErrorType;
  message: string;
  details?: string;
  action?: () => void;
  actionText?: string;
}

class ErrorHandler {
  private showToast(message: string): void {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    } else {
      // iOS 可以使用第三方库如 react-native-toast-message
      console.log('Toast:', message);
    }
  }

  showError(error: ErrorInfo): void {
    switch (error.type) {
      case ErrorType.PERMISSION_DENIED:
        this.handlePermissionDenied(error);
        break;
      case ErrorType.PERMISSION_BLOCKED:
        this.handlePermissionBlocked(error);
        break;
      case ErrorType.NETWORK_ERROR:
        this.handleNetworkError(error);
        break;
      case ErrorType.STORAGE_ERROR:
        this.handleStorageError(error);
        break;
      default:
        this.handleUnknownError(error);
        break;
    }
  }

  private handlePermissionDenied(error: ErrorInfo): void {
    Alert.alert(
      '权限被拒绝',
      error.message || '应用需要相册权限才能正常工作',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: error.actionText || '重新授权',
          onPress: error.action || (() => {}),
        },
      ],
    );
  }

  private handlePermissionBlocked(error: ErrorInfo): void {
    Alert.alert(
      '权限被永久拒绝',
      error.message || '请在设置中手动开启相册权限',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: error.actionText || '去设置',
          onPress: error.action || (() => {}),
        },
      ],
    );
  }

  private handleNetworkError(error: ErrorInfo): void {
    this.showToast(error.message || '网络连接失败，请检查网络设置');
    
    if (error.action) {
      Alert.alert(
        '网络错误',
        error.message || '网络连接失败',
        [
          {
            text: '取消',
            style: 'cancel',
          },
          {
            text: error.actionText || '重试',
            onPress: error.action,
          },
        ],
      );
    }
  }

  private handleStorageError(error: ErrorInfo): void {
    Alert.alert(
      '存储错误',
      error.message || '访问设备存储时发生错误',
      [
        {
          text: '确定',
          onPress: error.action,
        },
      ],
    );
  }

  private handleUnknownError(error: ErrorInfo): void {
    console.error('未知错误:', error);
    
    Alert.alert(
      '发生错误',
      error.message || '应用遇到了未知错误',
      [
        {
          text: '确定',
          onPress: error.action,
        },
      ],
    );
  }

  showSuccess(message: string): void {
    this.showToast(message);
  }

  showInfo(message: string): void {
    this.showToast(message);
  }

  // 便捷方法
  static permissionDenied(message?: string, onRetry?: () => void): void {
    new ErrorHandler().showError({
      type: ErrorType.PERMISSION_DENIED,
      message: message || '相册权限被拒绝',
      action: onRetry,
      actionText: '重新授权',
    });
  }

  static permissionBlocked(message?: string, onOpenSettings?: () => void): void {
    new ErrorHandler().showError({
      type: ErrorType.PERMISSION_BLOCKED,
      message: message || '相册权限被永久拒绝，请在设置中手动开启',
      action: onOpenSettings,
      actionText: '去设置',
    });
  }

  static networkError(message?: string, onRetry?: () => void): void {
    new ErrorHandler().showError({
      type: ErrorType.NETWORK_ERROR,
      message: message || '网络连接失败',
      action: onRetry,
      actionText: '重试',
    });
  }

  static storageError(message?: string): void {
    new ErrorHandler().showError({
      type: ErrorType.STORAGE_ERROR,
      message: message || '访问设备存储时发生错误',
    });
  }

  static unknownError(message?: string, details?: string): void {
    new ErrorHandler().showError({
      type: ErrorType.UNKNOWN_ERROR,
      message: message || '发生未知错误',
      details,
    });
  }
}

export default ErrorHandler;
