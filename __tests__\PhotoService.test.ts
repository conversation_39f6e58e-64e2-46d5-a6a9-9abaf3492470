import PhotoService from '../src/services/PhotoService';
import PermissionService from '../src/services/PermissionService';

// Mock the dependencies
jest.mock('@react-native-camera-roll/camera-roll', () => ({
  CameraRoll: {
    getPhotos: jest.fn(),
  },
}));

jest.mock('../src/services/PermissionService', () => ({
  ensurePhotoPermission: jest.fn(),
}));

describe('PhotoService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPhotos', () => {
    it('should return empty array when permission is denied', async () => {
      // Mock permission denied
      (PermissionService.ensurePhotoPermission as jest.Mock).mockResolvedValue({
        granted: false,
        message: 'Permission denied',
      });

      const result = await PhotoService.getPhotos();

      expect(result.photos).toEqual([]);
      expect(result.hasNextPage).toBe(false);
      expect(result.error).toBe('Permission denied');
    });

    it('should return photos when permission is granted', async () => {
      // Mock permission granted
      (PermissionService.ensurePhotoPermission as jest.Mock).mockResolvedValue({
        granted: true,
      });

      // Mock CameraRoll response
      const mockCameraRollResponse = {
        edges: [
          {
            node: {
              image: {
                uri: 'file://test1.jpg',
                filename: 'test1.jpg',
                width: 1920,
                height: 1080,
              },
              timestamp: 1640995200,
              type: 'image/jpeg',
            },
          },
        ],
        page_info: {
          has_next_page: false,
          end_cursor: 'cursor1',
        },
      };

      const {CameraRoll} = require('@react-native-camera-roll/camera-roll');
      CameraRoll.getPhotos.mockResolvedValue(mockCameraRollResponse);

      const result = await PhotoService.getPhotos();

      expect(result.photos).toHaveLength(1);
      expect(result.photos[0]).toEqual({
        uri: 'file://test1.jpg',
        filename: 'test1.jpg',
        width: 1920,
        height: 1080,
        timestamp: 1640995200,
        type: 'image/jpeg',
      });
      expect(result.hasNextPage).toBe(false);
      expect(result.endCursor).toBe('cursor1');
      expect(result.error).toBeUndefined();
    });
  });

  describe('autoSyncPhotos', () => {
    it('should automatically sync photos with default limit', async () => {
      // Mock permission granted
      (PermissionService.ensurePhotoPermission as jest.Mock).mockResolvedValue({
        granted: true,
      });

      // Mock CameraRoll response
      const mockCameraRollResponse = {
        edges: Array.from({length: 20}, (_, i) => ({
          node: {
            image: {
              uri: `file://test${i}.jpg`,
              filename: `test${i}.jpg`,
              width: 1920,
              height: 1080,
            },
            timestamp: 1640995200 + i,
            type: 'image/jpeg',
          },
        })),
        page_info: {
          has_next_page: true,
          end_cursor: 'cursor1',
        },
      };

      const {CameraRoll} = require('@react-native-camera-roll/camera-roll');
      CameraRoll.getPhotos.mockResolvedValue(mockCameraRollResponse);

      const result = await PhotoService.autoSyncPhotos();

      expect(result.photos.length).toBeGreaterThan(0);
      expect(result.error).toBeUndefined();
    });
  });
});
