# 相册自动获取应用 (PhotoGalleryApp)

这是一个React Native应用，可以在用户授权相册权限后自动获取手机相册中的照片，无需人工干预。

## 功能特性

- ✅ 自动请求相册访问权限
- ✅ 权限授权后自动获取相册照片
- ✅ 支持分页加载，避免内存溢出
- ✅ 下拉刷新重新同步相册
- ✅ 网格布局显示照片
- ✅ 点击照片查看详细信息
- ✅ 完善的错误处理和用户提示
- ✅ 支持iOS和Android平台

## 技术栈

- React Native 0.72.6
- TypeScript
- @react-native-camera-roll/camera-roll - 相册访问
- react-native-permissions - 权限管理
- @react-navigation/native - 导航

## 安装和运行

### 前置要求

- Node.js >= 16
- React Native CLI
- Android Studio (Android开发)
- Xcode (iOS开发)

### 安装依赖

```bash
npm install
```

### iOS设置

```bash
cd ios && pod install && cd ..
```

### 运行应用

#### Android
```bash
npm run android
```

#### iOS
```bash
npm run ios
```

## 权限说明

### Android权限
- `READ_EXTERNAL_STORAGE` - 读取外部存储（Android 12及以下）
- `READ_MEDIA_IMAGES` - 读取媒体图片（Android 13+）

### iOS权限
- `NSPhotoLibraryUsageDescription` - 访问相册权限

## 应用流程

1. **启动应用** - 应用启动后自动检查相册权限
2. **权限请求** - 如果没有权限，自动弹出权限请求对话框
3. **自动同步** - 权限授权后立即自动获取相册照片（默认最近100张）
4. **显示照片** - 以3列网格布局显示照片
5. **分页加载** - 滚动到底部自动加载更多照片
6. **手动刷新** - 支持下拉刷新重新同步相册

## 核心组件

### PermissionService
负责处理相册权限的请求和检查：
- 自动检测平台（iOS/Android）
- 处理不同Android版本的权限差异
- 提供友好的权限被拒绝提示

### PhotoService
负责相册照片的获取和管理：
- 自动同步相册照片
- 分页加载避免内存问题
- 获取照片元数据（尺寸、时间戳等）

### PhotoGalleryScreen
主界面组件：
- 响应式网格布局
- 下拉刷新功能
- 无限滚动加载
- 错误状态处理

### ErrorHandler
统一的错误处理和用户反馈：
- 不同类型错误的分类处理
- 友好的用户提示
- 引导用户解决问题

## 自定义配置

### 修改获取照片数量
在 `src/services/PhotoService.ts` 中修改：
```typescript
// 修改每页加载数量
private pageSize = 20;

// 修改自动同步的最大照片数
async autoSyncPhotos(): Promise<PhotosResult> {
  return this.getAllPhotos(100); // 修改这个数字
}
```

### 修改网格列数
在 `src/screens/PhotoGalleryScreen.tsx` 中修改：
```typescript
const numColumns = 3; // 修改列数
```

## 错误处理

应用包含完善的错误处理机制：

- **权限被拒绝** - 提示用户重新授权
- **权限被永久拒绝** - 引导用户到设置页面手动开启
- **相册访问失败** - 显示具体错误信息
- **网络错误** - 提供重试选项

## 注意事项

1. **Android 13+** - 使用新的细分权限 `READ_MEDIA_IMAGES`
2. **iOS** - 需要在Info.plist中配置权限描述
3. **性能优化** - 使用分页加载避免一次性加载过多照片
4. **内存管理** - 图片使用缩略图显示，避免内存溢出

## 开发和调试

### 查看日志
```bash
# Android
npx react-native log-android

# iOS
npx react-native log-ios
```

### 清理缓存
```bash
npx react-native start --reset-cache
```

## 构建发布版本

### Android
```bash
npm run build:android
```

### iOS
```bash
npm run build:ios
```

## 许可证

MIT License
