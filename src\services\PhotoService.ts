import {CameraRoll, PhotoIdentifier, GetPhotosParams} from '@react-native-camera-roll/camera-roll';
import PermissionService from './PermissionService';

export interface PhotoItem {
  uri: string;
  filename?: string;
  width: number;
  height: number;
  timestamp: number;
  type: string;
}

export interface PhotosResult {
  photos: PhotoItem[];
  hasNextPage: boolean;
  endCursor?: string;
  error?: string;
}

class PhotoService {
  private pageSize = 20;

  async getPhotos(after?: string, first: number = this.pageSize): Promise<PhotosResult> {
    try {
      // 确保有相册权限
      const permissionResult = await PermissionService.ensurePhotoPermission();
      if (!permissionResult.granted) {
        return {
          photos: [],
          hasNextPage: false,
          error: permissionResult.message || '没有相册权限',
        };
      }

      const params: GetPhotosParams = {
        first,
        assetType: 'Photos',
        groupTypes: 'All',
        include: ['filename', 'imageSize', 'playableDuration'],
      };

      if (after) {
        params.after = after;
      }

      const result = await CameraRoll.getPhotos(params);
      
      const photos: PhotoItem[] = result.edges.map((edge: PhotoIdentifier) => ({
        uri: edge.node.image.uri,
        filename: edge.node.image.filename,
        width: edge.node.image.width,
        height: edge.node.image.height,
        timestamp: edge.node.timestamp,
        type: edge.node.type,
      }));

      return {
        photos,
        hasNextPage: result.page_info.has_next_page,
        endCursor: result.page_info.end_cursor,
      };
    } catch (error) {
      console.error('获取相册照片失败:', error);
      return {
        photos: [],
        hasNextPage: false,
        error: '获取相册照片时发生错误',
      };
    }
  }

  async getAllPhotos(maxCount: number = 100): Promise<PhotosResult> {
    try {
      const allPhotos: PhotoItem[] = [];
      let hasNextPage = true;
      let endCursor: string | undefined;
      let totalFetched = 0;

      while (hasNextPage && totalFetched < maxCount) {
        const remainingCount = Math.min(this.pageSize, maxCount - totalFetched);
        const result = await this.getPhotos(endCursor, remainingCount);
        
        if (result.error) {
          return result;
        }

        allPhotos.push(...result.photos);
        hasNextPage = result.hasNextPage;
        endCursor = result.endCursor;
        totalFetched += result.photos.length;

        // 如果这次获取的照片数量少于请求数量，说明已经到底了
        if (result.photos.length < remainingCount) {
          hasNextPage = false;
        }
      }

      return {
        photos: allPhotos,
        hasNextPage: totalFetched >= maxCount && hasNextPage,
        endCursor,
      };
    } catch (error) {
      console.error('获取所有照片失败:', error);
      return {
        photos: [],
        hasNextPage: false,
        error: '获取所有照片时发生错误',
      };
    }
  }

  async getRecentPhotos(count: number = 20): Promise<PhotosResult> {
    return this.getPhotos(undefined, count);
  }

  async autoSyncPhotos(): Promise<PhotosResult> {
    console.log('开始自动同步相册...');
    
    // 自动获取最近的100张照片
    const result = await this.getAllPhotos(100);
    
    if (result.error) {
      console.error('自动同步失败:', result.error);
    } else {
      console.log(`自动同步完成，获取到 ${result.photos.length} 张照片`);
    }
    
    return result;
  }
}

export default new PhotoService();
